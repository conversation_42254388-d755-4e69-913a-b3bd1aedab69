# AWS Console Deployment Guide

This guide provides step-by-step instructions for deploying the Windows Server 2022 base image with .NET 4.8, registry optimizations, and firewall rules using the AWS Console.

> **Note**: This guide covers **ImageBuilder AMI creation only**. For EC2 instance deployment, see the scripts in `../EC2-Deployment/` folder.

## Prerequisites

- AWS Account with appropriate permissions for ImageBuilder
- VPC with private subnet and internet gateway/NAT gateway
- Security groups configured for Image Builder build instances
- S3 bucket for business configurations (optional, for instance deployment)
- arn:aws:imagebuilder:af-south-1:aws:image/windows-server-2022-english-full-base-x86/2025.8.13

## Part 1: Create Base AMI with Image Builder

### Step 1: Create Custom Component (if needed)

1. **Navigate to Image Builder**:
   - Go to **EC2 Image Builder** in AWS Console
   - Ensure you're in the **af-south-1** region

2. **Create Custom Components**:

   **a) .NET 4.8 Component**:
   - Click **Components** > **Create component**
   - **Component type**: Build
   - **Name**: `InstallDotNet48`
   - **Version**: `1.0.0`
   - **Description**: `Install Microsoft .NET Framework 4.8 on Windows Server`
   - **Platform**: Windows
   - **Document format**: YAML
   - Copy content from `components/install-dotnet48.yml` and paste
   - Click **Create component**

   **b) Registry Tweaks Component**:
   - Click **Components** > **Create component**
   - **Component type**: Build
   - **Name**: `ConfigureRegistryTweaks`
   - **Version**: `1.0.0`
   - **Description**: `Apply Windows registry optimizations and security configurations`
   - **Platform**: Windows
   - **Document format**: YAML
   - Copy content from `components/configure-registry-tweaks.yml` and paste
   - Click **Create component**

   **c) Firewall Rules Component**:
   - Click **Components** > **Create component**
   - **Component type**: Build
   - **Name**: `ConfigureFirewallRules`
   - **Version**: `1.0.0`
   - **Description**: `Configure Windows Firewall rules for business server environments`
   - **Platform**: Windows
   - **Document format**: YAML
   - Copy content from `components/configure-firewall-rules.yml` and paste
   - Click **Create component**

### Step 2: Create Image Recipe

1. **Navigate to Image Recipes**:
   - Click **Image recipes** > **Create image recipe**

2. **Recipe Configuration**:
   - **Name**: `WindowsServer2022Base`
   - **Version**: `1.0.0`
   - **Description**: `Windows Server 2022 base image with .NET 4.8, registry optimizations, and firewall rules for business deployment`

3. **Base Image Selection**:
   - **Image type**: AWS managed images
   - **Image origin**: Quick start (Amazon-managed)
   - **Image name**: Select **Windows Server 2022 English Full Base** (latest)
   - 📋 **For image type explanations, see [WINDOWS_SERVER_2022_IMAGES_EXPLAINED.md](WINDOWS_SERVER_2022_IMAGES_EXPLAINED.md)**

4. **Build Components**:
   - Click **Add component**
   - Add in this order:
     1. **update-windows** (AWS managed) - Version: Latest
     2. **InstallDotNet48** (Your component) - Version: 1.0.0
     3. **ConfigureRegistryTweaks** (Your component) - Version: 1.0.0
     4. **ConfigureFirewallRules** (Your component) - Version: 1.0.0
     5. **reboot-windows** (AWS managed) - Version: Latest

5. **Additional Settings**:
   - **Working directory**: `C:\ImageBuilder`
   - **Tags**: Add relevant tags
   - Click **Create image recipe**

### Step 3: Create Infrastructure Configuration

1. **Navigate to Infrastructure Configurations**:
   - Click **Infrastructure configurations** > **Create infrastructure configuration**

2. **Configuration Details**:
   - **Name**: `WindowsServer2022BuildInfrastructure`
   - **Description**: `Infrastructure for building Windows Server 2022 base images`

3. **Instance Configuration**:
   - **Instance type**: `m5.large` (minimum recommended)
   - **Instance profile**: Create or select IAM role with:
     - `EC2InstanceProfileForImageBuilder` policy
     - S3 access for logs (if using S3 logging)
     

     
4. **Network Settings**:
   - **VPC**: Select your VPC
   - **Subnet**: Select private subnet with NAT gateway access
   - **Security groups**: Select security group allowing:
     - Outbound HTTPS (443) for downloads
     - Outbound HTTP (80) for package repositories
     - Outbound DNS (53)

5. **Additional Settings**:
   - **Key pair**: Select for troubleshooting access
   - **Terminate instance on failure**: Yes
   - **SNS topic**: Optional for notifications
   - Click **Create infrastructure configuration**

### Step 4: Create Distribution Configuration

1. **Navigate to Distribution Configurations**:
   - Click **Distribution configurations** > **Create distribution configuration**

2. **Configuration Details**:
   - **Name**: `WindowsServer2022Distribution`
   - **Description**: `Distribution configuration for Windows Server 2022 base AMI`

3. **Distribution Settings**:
   - **Region**: `af-south-1`
   - **AMI name**: `BaseWindowsServer2022-{imagebuilder:buildDate}`
   - **AMI description**: `Windows Server 2022 base image with .NET 4.8 for business deployment - Built on {imagebuilder:buildDate}`

4. **AMI Tags**:
   - `Name`: `BaseWindowsServer2022-{imagebuilder:buildDate}`
   - `OS`: `Windows Server 2022`
   - `Components`: `.NET Framework 4.8`
   - `Environment`: `Production`
   - `CreatedBy`: `AWS Image Builder`

5. **Target Accounts** (if sharing):
   - Add AWS account IDs that should have access
   - Set launch permissions as needed

6. **Create Configuration**:
   - Click **Create distribution configuration**

### Step 5: Create Image Pipeline

1. **Navigate to Image Pipelines**:
   - Click **Image pipelines** > **Create image pipeline**

2. **Pipeline Configuration**:
   - **Name**: `WindowsServer2022BasePipeline`
   - **Description**: `Pipeline for building Windows Server 2022 base AMI with .NET 4.8`

3. **Recipe Selection**:
   - **Image recipe**: Select `WindowsServer2022Base`

4. **Infrastructure Configuration**:
   - Select `WindowsServer2022BuildInfrastructure`

5. **Distribution Configuration**:
   - Select `WindowsServer2022Distribution`

6. **Image Testing**:
   - **Enable image testing**: Yes
   - **Timeout**: 720 minutes (12 hours)

7. **Schedule** (optional):
   - **Schedule**: Weekly
   - **Day**: Sunday
   - **Time**: 02:00 UTC
   - **Pipeline execution start condition**: Expression match and dependency updates available

8. **Tags**:
   - Add relevant pipeline tags
   - Click **Create image pipeline**

### Step 6: Run Pipeline

1. **Execute Pipeline**:
   - Select your pipeline: `WindowsServer2022BasePipeline`
   - Click **Actions** > **Run pipeline**
   - Confirm execution

2. **Monitor Progress**:
   - Click on the pipeline name
   - Go to **Executions** tab
   - Monitor build progress (typically 45-90 minutes)

3. **Verify Completion**:
   - Check execution status shows **Available**
   - Note the AMI ID created
   - Verify AMI appears in **EC2** > **AMIs**

## Part 2: Setup Business Configurations

### Step 1: Create S3 Bucket

1. **Navigate to S3**:
   - Go to **S3** in AWS Console

2. **Create Bucket**:
   - Click **Create bucket**
   - **Bucket name**: `your-business-configs-bucket` (choose unique name)
   - **Region**: `Africa (Cape Town) af-south-1`
   - **Block Public Access**: Keep all settings enabled
   - Click **Create bucket**

### Step 2: Upload Configuration Files

1. **Open Bucket**:
   - Click on your bucket name

2. **Create Folder Structure**:
   - Click **Create folder**
   - **Folder name**: `business-configs`
   - Click **Create folder**

3. **Upload Files**:
   - Click on `business-configs` folder
   - Click **Upload**
   - **Add files**: Select `SPF_PRD.json` and `STM_PRD.json` from your `configs/` directory
   - Click **Upload**

4. **Verify Upload**:
   - Confirm files are at: `s3://your-bucket/business-configs/SPF_PRD.json`
   - Confirm files are at: `s3://your-bucket/business-configs/STM_PRD.json`

### Step 3: Create IAM Role for EC2 Instances

1. **Navigate to IAM**:
   - Go to **IAM** > **Roles** > **Create role**

2. **Role Configuration**:
   - **Trusted entity**: AWS service
   - **Service**: EC2
   - Click **Next**

3. **Permissions**:
   - Click **Create policy**
   - **JSON** tab, paste:
   ```json
   {
       "Version": "2012-10-17",
       "Statement": [
           {
               "Effect": "Allow",
               "Action": [
                   "s3:GetObject"
               ],
               "Resource": [
                   "arn:aws:s3:::your-business-configs-bucket/business-configs/*"
               ]
           },
           {
               "Effect": "Allow",
               "Action": [
                   "ec2:DescribeTags"
               ],
               "Resource": "*"
           }
       ]
   }
   ```
   - **Name**: `EC2-BusinessConfig-S3-Access`
   - Create policy and attach to role

4. **Role Details**:
   - **Role name**: `EC2-BusinessConfig-Role`
   - **Description**: `Allows EC2 instances to read business configurations from S3`
   - Create role

## Part 3: Next Steps - EC2 Instance Deployment

After successfully creating your custom AMI with ImageBuilder, you can deploy EC2 instances using the business configurations.

### AMI Verification

1. **Check AMI Status**:
   - Go to **EC2** > **AMIs**
   - Verify your `BaseWindowsServer2022-*` AMI shows as "Available"
   - Note the AMI ID for instance deployment

2. **AMI Details**:
   - **Components**: .NET Framework 4.8, Registry Optimizations, Firewall Rules
   - **Region**: af-south-1
   - **Architecture**: x86_64 (64-bit)

### Instance Deployment Options

**Option 1: Manual Console Deployment**
1. Go to **EC2** > **Launch instances**
2. Select your custom `BaseWindowsServer2022-*` AMI
3. Configure instance with appropriate business tags (`ClientTag`, `BusinessUnit`, etc.)
4. Use user data from `templates/user-data-template.ps1`
5. Ensure IAM role has S3 permissions for business config download

**Option 2: Automated Deployment Scripts** (Recommended)
Use the scripts in `../EC2-Deployment/` folder for streamlined deployment:

```powershell
# Generate custom user data for specific business
..\EC2-Deployment\generate-user-data.ps1 -BusinessConfigPath "configs\SPF_PRD.json" -ServerRole "MSSQL-2022"

# Launch instance with complete business configuration
..\EC2-Deployment\launch-business-instance.ps1 -BusinessConfigPath "configs\SPF_PRD.json" -ServerRole "MSSQL-2022" -AMIId "ami-xxxxxxxxx" -InstanceType "t3.medium" -KeyName "my-key" -SecurityGroupId "sg-xxxxxxxxx" -SubnetId "subnet-xxxxxxxxx"

# Setup automatic AMI updates on deployed instance
..\EC2-Deployment\schedule-auto-updates.ps1 -ScheduleType "Weekly" -ScheduleDay "Sunday"
```

📋 **For detailed instance deployment instructions, see `../EC2-Deployment/README.md`**

## Troubleshooting

### Common ImageBuilder Issues

1. **Pipeline Build Fails**:
   - Check Image Builder execution logs in the console
   - Verify security group allows outbound internet access (ports 80, 443)
   - Ensure IAM role has required permissions for ImageBuilder
   - Check if base AMI is available in af-south-1 region

2. **Component Creation Fails**:
   - Verify YAML syntax in component definitions
   - Check component names don't conflict with existing components
   - Ensure component version is properly incremented

3. **Recipe Build Fails**:
   - Verify all referenced components exist
   - Check component order (dependencies)
   - Ensure base image is accessible

4. **Distribution Fails**:
   - Verify target account IDs are correct
   - Check AMI naming conflicts
   - Ensure distribution region is correct (af-south-1)

### Log Locations

- **ImageBuilder Logs**: EC2 Image Builder Console > Pipelines > Executions > View details
- **Build Instance Logs**: Available during build execution
- **CloudWatch Logs**: If logging is configured in infrastructure settings

## Next Steps

After successful AMI creation:
1. **Deploy EC2 Instances**: Use scripts in `../EC2-Deployment/` folder
2. **Configure Business Settings**: Apply domain joining and business-specific software
3. **Set up Monitoring**: Implement CloudWatch monitoring and logging
4. **Security Hardening**: Apply additional security policies as needed
5. **Documentation**: Document the AMI version and deployment process
6. **Testing**: Validate the AMI with test deployments before production use

## Alternative Deployment Methods

- **AWS CLI Deployment**: See `AWS_CLI_DEPLOYMENT_GUIDE.md` for command-line deployment
- **Automated Scripts**: Use `deployment/deploy.ps1` for fully automated deployment

## Related Documentation

- **EC2 Instance Deployment**: `../EC2-Deployment/README.md`
- **Registry and Firewall Configuration**: `REGISTRY_AND_FIREWALL_CONFIGURATION.md`
- **Business Deployment Guide**: `BUSINESS_DEPLOYMENT_GUIDE.md`
- **AWS CLI Deployment**: `AWS_CLI_DEPLOYMENT_GUIDE.md`
