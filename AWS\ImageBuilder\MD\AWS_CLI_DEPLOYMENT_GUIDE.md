# AWS CLI Deployment Guide

This guide provides step-by-step instructions for deploying the Windows Server 2022 base image with .NET 4.8, registry optimizations, and firewall rules using the AWS CLI.

> **Note**: This guide covers **ImageBuilder AMI creation only**. For EC2 instance deployment, see the scripts in `../EC2-Deployment/` folder.

## Prerequisites

- AWS CLI installed and configured
- Appropriate IAM permissions for ImageBuilder
- PowerShell (for Windows) or Bash (for Linux/macOS)
- VPC with private subnet and internet gateway/NAT gateway
- Security groups configured for Image Builder build instances

## AWS CLI Configuration

### 1. Configure AWS CLI
```bash
# Configure AWS CLI with your credentials
aws configure

# Verify configuration
aws sts get-caller-identity

# Set default region
aws configure set region af-south-1
```

### 2. Verify Permissions
```bash
# Test ImageBuilder permissions
aws imagebuilder list-components --max-results 5
aws imagebuilder list-image-recipes --max-results 5
```

## Step-by-Step Deployment

### Step 1: Create Custom Components

#### Create .NET 4.8 Component
```bash
aws imagebuilder create-component \
    --name "InstallDotNet48" \
    --semantic-version "1.0.0" \
    --description "Install Microsoft .NET Framework 4.8 on Windows Server" \
    --platform "Windows" \
    --data file://components/install-dotnet48.yml \
    --tags Environment=Production,Component=DotNet48
```

#### Create Registry Tweaks Component
```bash
aws imagebuilder create-component \
    --name "ConfigureRegistryTweaks" \
    --semantic-version "1.0.0" \
    --description "Apply Windows registry optimizations and security configurations" \
    --platform "Windows" \
    --data file://components/configure-registry-tweaks.yml \
    --tags Environment=Production,Component=RegistryTweaks
```

#### Create Firewall Rules Component
```bash
aws imagebuilder create-component \
    --name "ConfigureFirewallRules" \
    --semantic-version "1.0.0" \
    --description "Configure Windows Firewall rules for business server environments" \
    --platform "Windows" \
    --data file://components/configure-firewall-rules.yml \
    --tags Environment=Production,Component=FirewallRules
```

### Step 2: Create Infrastructure Configuration

#### Update Infrastructure Configuration File
Edit `infrastructure/build-infrastructure.yml` with your specific values:
- Replace `sg-xxxxxxxxx` with your security group ID
- Replace `subnet-xxxxxxxxx` with your subnet ID
- Replace `your-key-pair` with your EC2 key pair name

```bash
aws imagebuilder create-infrastructure-configuration \
    --cli-input-yaml file://infrastructure/build-infrastructure.yml
```

### Step 3: Create Distribution Configuration

#### Update Distribution Configuration File
Edit `distribution/distribution-config.yml` with your account IDs and preferences.

```bash
aws imagebuilder create-distribution-configuration \
    --cli-input-yaml file://distribution/distribution-config.yml
```

### Step 4: Create Image Recipe

#### Update Recipe File
Verify `recipes/windows-server-2022-custom.yml` references the correct components.

```bash
aws imagebuilder create-image-recipe \
    --cli-input-yaml file://recipes/windows-server-2022-custom.yml
```

### Step 5: Create Image Pipeline

#### Update Pipeline Configuration
Edit `pipelines/windows-server-pipeline.yml` with the correct ARNs from previous steps.

```bash
aws imagebuilder create-image-pipeline \
    --cli-input-yaml file://pipelines/windows-server-pipeline.yml
```

### Step 6: Start Pipeline Execution

```bash
# Get pipeline ARN
PIPELINE_ARN=$(aws imagebuilder list-image-pipelines \
    --filters name=name,values=WindowsServer2022BasePipeline \
    --query 'imagePipelineList[0].arn' \
    --output text)

# Start pipeline execution
aws imagebuilder start-image-pipeline-execution \
    --image-pipeline-arn $PIPELINE_ARN
```

## Automated Deployment Script

### Using the Provided Deploy Script

The `deployment/deploy.ps1` script automates the entire process:

```powershell
# PowerShell - Full automated deployment
.\deployment\deploy.ps1 -Region "af-south-1" -SecurityGroupId "sg-xxxxxxxxx" -SubnetId "subnet-xxxxxxxxx" -KeyPairName "your-key-pair" -StartBuild
```

#### Script Parameters
- `-Region`: AWS region (default: af-south-1)
- `-SecurityGroupId`: Security group for build instances
- `-SubnetId`: Subnet for build instances
- `-KeyPairName`: EC2 key pair for troubleshooting access
- `-S3LogsBucket`: S3 bucket for build logs (optional)
- `-SnsTopicArn`: SNS topic for notifications (optional)
- `-TargetAccountIds`: Additional AWS accounts for AMI sharing
- `-CreateIAMRole`: Create IAM role automatically
- `-StartBuild`: Start pipeline execution immediately

### Example Full Deployment
```powershell
.\deployment\deploy.ps1 `
    -Region "af-south-1" `
    -SecurityGroupId "sg-0123456789abcdef0" `
    -SubnetId "subnet-0123456789abcdef0" `
    -KeyPairName "my-imagebuilder-key" `
    -S3LogsBucket "my-imagebuilder-logs" `
    -CreateIAMRole `
    -StartBuild
```

## Monitoring and Management

### Monitor Pipeline Execution
```bash
# List pipeline executions
aws imagebuilder list-image-pipeline-executions \
    --image-pipeline-arn $PIPELINE_ARN

# Get execution details
EXECUTION_ARN=$(aws imagebuilder list-image-pipeline-executions \
    --image-pipeline-arn $PIPELINE_ARN \
    --query 'imagePipelineExecutions[0].arn' \
    --output text)

aws imagebuilder get-image-pipeline-execution \
    --image-pipeline-execution-arn $EXECUTION_ARN
```

### Check Build Status
```bash
# List recent images
aws imagebuilder list-images \
    --filters name=name,values=WindowsServer2022Base \
    --query 'imageVersionList[*].[name,version,state.status,dateCreated]' \
    --output table
```

### View Build Logs
```bash
# Get image ARN from execution
IMAGE_ARN=$(aws imagebuilder get-image-pipeline-execution \
    --image-pipeline-execution-arn $EXECUTION_ARN \
    --query 'imagePipelineExecution.imageArn' \
    --output text)

# Get build details
aws imagebuilder get-image \
    --image-build-version-arn $IMAGE_ARN
```

## Updating and Maintenance

### Update Base AMI
```bash
# Check for newer base AMI
aws ec2 describe-images \
    --owners amazon \
    --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" \
    --query 'Images | sort_by(@, &CreationDate) | [-1].[ImageId,Name,CreationDate]' \
    --output table

# Trigger pipeline rebuild (uses latest base AMI automatically)
aws imagebuilder start-image-pipeline-execution \
    --image-pipeline-arn $PIPELINE_ARN
```

### Update Components
```bash
# Create new component version
aws imagebuilder create-component \
    --name "InstallDotNet48" \
    --semantic-version "1.0.1" \
    --description "Install Microsoft .NET Framework 4.8 on Windows Server - Updated" \
    --platform "Windows" \
    --data file://components/install-dotnet48.yml

# Update recipe to use new component version
# Edit recipes/windows-server-2022-custom.yml and increment version
aws imagebuilder create-image-recipe \
    --cli-input-yaml file://recipes/windows-server-2022-custom.yml
```

## Cleanup and Resource Management

### Using the Cleanup Script
```powershell
# Remove all ImageBuilder resources
.\deployment\cleanup.ps1 -Region "af-south-1" -DeleteAMIs -Force
```

### Manual Cleanup
```bash
# Delete pipeline
aws imagebuilder delete-image-pipeline \
    --image-pipeline-arn $PIPELINE_ARN

# Delete recipe
aws imagebuilder delete-image-recipe \
    --image-recipe-arn "arn:aws:imagebuilder:af-south-1:123456789012:image-recipe/windowsserver2022base/1.0.0"

# Delete components
aws imagebuilder delete-component \
    --component-build-version-arn "arn:aws:imagebuilder:af-south-1:123456789012:component/installdotnet48/1.0.0"

# Delete infrastructure configuration
aws imagebuilder delete-infrastructure-configuration \
    --infrastructure-configuration-arn "arn:aws:imagebuilder:af-south-1:123456789012:infrastructure-configuration/windowsserver2022buildinfrastructure"

# Delete distribution configuration
aws imagebuilder delete-distribution-configuration \
    --distribution-configuration-arn "arn:aws:imagebuilder:af-south-1:123456789012:distribution-configuration/windowsserver2022distribution"
```

## Troubleshooting

### Common CLI Issues

1. **Permission Denied**:
   ```bash
   # Check IAM permissions
   aws iam get-user
   aws iam list-attached-user-policies --user-name your-username
   ```

2. **Component Creation Fails**:
   ```bash
   # Validate YAML syntax
   aws imagebuilder create-component --generate-cli-skeleton > template.json
   # Edit template.json and test
   ```

3. **Pipeline Execution Fails**:
   ```bash
   # Check execution logs
   aws logs describe-log-groups --log-group-name-prefix "/aws/imagebuilder"
   ```

### Validation Commands
```bash
# Verify all resources exist
aws imagebuilder list-components --owner Self
aws imagebuilder list-image-recipes --owner Self
aws imagebuilder list-infrastructure-configurations
aws imagebuilder list-distribution-configurations
aws imagebuilder list-image-pipelines
```

## Best Practices

1. **Use Version Control**: Keep all YAML files in version control
2. **Test Components**: Test individual components before pipeline deployment
3. **Monitor Costs**: ImageBuilder charges for build time and storage
4. **Automate Updates**: Use scheduled pipelines for regular updates
5. **Tag Resources**: Apply consistent tagging for cost tracking and management
6. **Security**: Use least privilege IAM policies
7. **Logging**: Enable CloudWatch logging for troubleshooting

## Next Steps

After successful AMI creation:
1. **Deploy EC2 Instances**: Use scripts in `../EC2-Deployment/` folder
2. **Automate Deployments**: Integrate with CI/CD pipelines
3. **Monitor Usage**: Set up CloudWatch dashboards
4. **Cost Optimization**: Review and optimize build schedules

## Related Documentation

- **Console Deployment**: `CONSOLE_DEPLOYMENT_GUIDE.md`
- **EC2 Instance Deployment**: `../EC2-Deployment/README.md`
- **Registry and Firewall Configuration**: `REGISTRY_AND_FIREWALL_CONFIGURATION.md`
- **Business Deployment Guide**: `BUSINESS_DEPLOYMENT_GUIDE.md`
