# AWS Image Builder Component: Install CrowdStrike Falcon Agent
# This component installs the CrowdStrike Falcon agent on Windows Server
# Prerequisites: CrowdStrike Customer ID (CID) and installer package
# Required Environment Variables (via AWS Systems Manager Parameter Store):
#CROWDSTRIKE_CUSTOMER_ID - Your Crowdstrike customer ID
#CROWDSTRIKE_INSTALLER_URL - Custom URL for installer (optional)

name: win-server-crowdstrike-agent
description: Install CrowdStrike Falcon agent on Windows Server
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CheckExistingCrowdStrike
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Checking for existing CrowdStrike Falcon agent..."

                # Check if CrowdStrike service is already installed
                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if ($csService) {
                    Write-Host "CrowdStrike Falcon agent is already installed"
                    Write-Host "Service Status: $($csService.Status)"
                    
                    # Get agent version if available
                    try {
                        $csPath = "${env:ProgramFiles}\CrowdStrike\CSFalconService.exe"
                        if (Test-Path $csPath) {
                            $version = (Get-ItemProperty $csPath).VersionInfo.FileVersion
                            Write-Host "CrowdStrike Falcon version: $version"
                        }
                    } catch {
                        Write-Warning "Could not determine CrowdStrike version: $($_.Exception.Message)"
                    }
                    
                    exit 0
                } else {
                    Write-Host "CrowdStrike Falcon agent is not installed. Proceeding with installation..."
                }

      - name: ValidateParameters
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Validating CrowdStrike installation parameters..."

                # Check for required environment variables or parameters
                # These should be set via Systems Manager Parameter Store or environment variables
                $cid = $env:CROWDSTRIKE_CID
                $installerUrl = $env:CROWDSTRIKE_INSTALLER_URL

                if (-not $cid) {
                    Write-Error "CROWDSTRIKE_CID environment variable is required"
                    exit 1
                }

                if (-not $installerUrl) {
                    Write-Warning "CROWDSTRIKE_INSTALLER_URL not provided. Will attempt to use default download method."
                }

                Write-Host "CrowdStrike CID: $($cid.Substring(0,8))..." # Only show first 8 chars for security
                Write-Host "Validation completed successfully"

      - name: DownloadCrowdStrikeAgent
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Downloading CrowdStrike Falcon agent..."

                # Create temp directory
                $tempDir = "C:\temp\crowdstrike"
                if (!(Test-Path $tempDir)) {
                    New-Item -ItemType Directory -Path $tempDir -Force
                    Write-Host "Created temp directory: $tempDir"
                }

                $installerPath = "$tempDir\WindowsSensor.exe"
                $installerUrl = $env:CROWDSTRIKE_INSTALLER_URL

                if ($installerUrl) {
                    Write-Host "Downloading from provided URL..."
                    try {
                        # Download from provided URL (could be S3, internal repository, etc.)
                        Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
                        Write-Host "Downloaded CrowdStrike installer to: $installerPath"
                    } catch {
                        Write-Error "Failed to download CrowdStrike installer: $($_.Exception.Message)"
                        exit 1
                    }
                } else {
                    Write-Error "No installer URL provided. Please set CROWDSTRIKE_INSTALLER_URL environment variable or place installer in $installerPath"
                    exit 1
                }

                # Verify file exists and has reasonable size
                if (Test-Path $installerPath) {
                    $fileSize = (Get-Item $installerPath).Length
                    Write-Host "Installer file size: $([math]::Round($fileSize/1MB, 2)) MB"
                    
                    if ($fileSize -lt 1MB) {
                        Write-Error "Installer file appears to be too small. Download may have failed."
                        exit 1
                    }
                } else {
                    Write-Error "Installer file not found after download"
                    exit 1
                }

      - name: InstallCrowdStrikeAgent
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Installing CrowdStrike Falcon agent..."

                $tempDir = "C:\temp\crowdstrike"
                $installerPath = "$tempDir\WindowsSensor.exe"
                $cid = $env:CROWDSTRIKE_CID

                if (!(Test-Path $installerPath)) {
                    Write-Error "CrowdStrike installer not found at: $installerPath"
                    exit 1
                }

                # Install CrowdStrike with CID
                Write-Host "Running CrowdStrike installer with CID..."
                try {
                    $installArgs = @(
                        "/install"
                        "/quiet"
                        "/norestart"
                        "CID=$cid"
                    )
                    
                    $process = Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
                    
                    if ($process.ExitCode -eq 0) {
                        Write-Host "CrowdStrike Falcon agent installed successfully"
                    } else {
                        Write-Error "CrowdStrike installation failed with exit code: $($process.ExitCode)"
                        exit 1
                    }
                } catch {
                    Write-Error "Failed to install CrowdStrike: $($_.Exception.Message)"
                    exit 1
                }

      - name: VerifyInstallation
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Verifying CrowdStrike Falcon agent installation..."

                # Wait a moment for service to initialize
                Start-Sleep -Seconds 10

                # Check if service is installed and running
                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if ($csService) {
                    Write-Host "CrowdStrike Falcon service found"
                    Write-Host "Service Status: $($csService.Status)"
                    
                    if ($csService.Status -eq "Running") {
                        Write-Host "CrowdStrike Falcon agent is running successfully"
                    } else {
                        Write-Warning "CrowdStrike service is installed but not running. Status: $($csService.Status)"
                        
                        # Attempt to start the service
                        try {
                            Start-Service -Name "CSFalconService"
                            Write-Host "Successfully started CrowdStrike service"
                        } catch {
                            Write-Warning "Could not start CrowdStrike service: $($_.Exception.Message)"
                        }
                    }
                } else {
                    Write-Error "CrowdStrike Falcon service not found after installation"
                    exit 1
                }

                # Check for agent connectivity (optional)
                Write-Host "Installation verification completed"

      - name: CleanupTempFiles
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
            commands:
              - |
                Write-Host "Cleaning up temporary files..."

                $tempDir = "C:\temp\crowdstrike"
                if (Test-Path $tempDir) {
                    try {
                        Remove-Item -Path $tempDir -Recurse -Force
                        Write-Host "Cleaned up temp directory: $tempDir"
                    } catch {
                        Write-Warning "Could not clean up temp directory: $($_.Exception.Message)"
                    }
                }

  - name: validate
    steps:
      - name: ValidateCrowdStrikeService
        action: ExecutePowerShell
        inputs:
            commands:
              - |
                Write-Host "Final validation of CrowdStrike Falcon agent..."

                $csService = Get-Service -Name "CSFalconService" -ErrorAction SilentlyContinue
                if ($csService -and $csService.Status -eq "Running") {
                    Write-Host "✓ CrowdStrike Falcon agent is installed and running"
                    
                    # Get additional info if available
                    try {
                        $csPath = "${env:ProgramFiles}\CrowdStrike\CSFalconService.exe"
                        if (Test-Path $csPath) {
                            $version = (Get-ItemProperty $csPath).VersionInfo.FileVersion
                            Write-Host "✓ CrowdStrike Falcon version: $version"
                        }
                    } catch {
                        Write-Host "Could not retrieve version information"
                    }
                    
                    Write-Host "CrowdStrike Falcon agent validation completed successfully"
                } else {
                    Write-Error "CrowdStrike Falcon agent validation failed"
                    exit 1
                }
