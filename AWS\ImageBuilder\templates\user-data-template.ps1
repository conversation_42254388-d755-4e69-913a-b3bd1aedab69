# EC2 User Data Script Template for Business-Specific Deployment
# This script is executed when an EC2 instance launches from your base AMI

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope LocalMachine -Force

# Create scripts directory
if (!(Test-Path "C:\Scripts")) {
    New-Item -ItemType Directory -Path "C:\Scripts" -Force | Out-Null
}

# Create log file
$logFile = "C:\Scripts\user-data.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting AWS UserData script for business deployment"

# Enable WinRM
Write-Log "Enabling WinRM"
Enable-PSRemoting -Force -SkipNetworkProfileCheck

# Configure WinRM for HTTPS
Write-Log "Configuring WinRM"
winrm quickconfig -q
winrm set winrm/config/winrs '@{MaxMemoryPerShellMB="512"}'
winrm set winrm/config '@{MaxTimeoutms="1800000"}'
winrm set winrm/config/service '@{AllowUnencrypted="false"}'
winrm set winrm/config/service/auth '@{Basic="true"}'

# Create self-signed certificate for HTTPS
Write-Log "Creating self-signed certificate for WinRM HTTPS"
$cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
winrm create winrm/config/Listener?Address=*+Transport=HTTPS "@{Hostname=`"localhost`";CertificateThumbprint=`"$($cert.Thumbprint)`"}"

# Configure firewall for WinRM HTTPS
Write-Log "Configuring firewall for WinRM HTTPS"
netsh advfirewall firewall add rule name="WinRM-HTTPS" dir=in localport=5986 protocol=TCP action=allow

# Restart WinRM service
Write-Log "Restarting WinRM service"
Restart-Service winrm

Write-Log "WinRM configuration completed"

try {
    # Get client tag from user data or environment variable
    # This should be passed as part of the EC2 launch parameters
    $clientTag = $env:CLIENT_TAG
    if (!$clientTag) {
        # Try to get from EC2 instance tags
        try {
            $instanceId = (Invoke-RestMethod -Uri "http://***************/latest/meta-data/instance-id" -TimeoutSec 10)
            $region = (Invoke-RestMethod -Uri "http://***************/latest/meta-data/placement/region" -TimeoutSec 10)

            # Get instance tags
            $tags = aws ec2 describe-tags --filters "Name=resource-id,Values=$instanceId" "Name=key,Values=ClientTag" --region $region --query 'Tags[0].Value' --output text 2>$null
            if ($tags -and $tags -ne "None") {
                $clientTag = $tags
                Write-Log "Client tag retrieved from EC2 instance tags: $clientTag"
            }
        } catch {
            Write-Log "Could not retrieve client tag from instance metadata: $($_.Exception.Message)" "WARNING"
        }
    }

    if (!$clientTag) {
        $clientTag = "SPF"  # Default fallback
        Write-Log "No client tag found, using default: $clientTag" "WARNING"
    }

    Write-Log "Using client tag: $clientTag"

    # Download business configuration from S3
    $s3Bucket = "your-config-bucket"
    $configFileName = "${clientTag}_PRD.json"
    $s3Key = "business-configs/$configFileName"
    $localConfigPath = "C:\Scripts\$configFileName"

    Write-Log "Downloading business configuration from S3: s3://$s3Bucket/$s3Key"

    try {
        # Download config from S3
        aws s3 cp "s3://$s3Bucket/$s3Key" $localConfigPath --region $region 2>&1 | Out-Null

        if ($LASTEXITCODE -eq 0 -and (Test-Path $localConfigPath)) {
            Write-Log "Successfully downloaded business configuration from S3"

            # Load business configuration from downloaded JSON
            $businessConfigJson = Get-Content $localConfigPath -Raw | ConvertFrom-Json
            $businessConfig = @{
                businessName = $businessConfigJson.businessName
                business_unit = $businessConfigJson.business_unit
                domain = $businessConfigJson.domain
                basePath = $businessConfigJson.basePath
                serverOUs = @{}
                ad_domain = $businessConfigJson.ad_domain
                environment = $businessConfigJson.environment
                client_tag = $businessConfigJson.client_tag
            }

            # Convert serverOUs to hashtable
            $businessConfigJson.serverOUs.PSObject.Properties | ForEach-Object {
                $businessConfig.serverOUs[$_.Name] = $_.Value
            }

            Write-Log "Business configuration loaded for: $($businessConfig.businessName)"

        } else {
            throw "Failed to download or file not found"
        }

    } catch {
        Write-Log "Failed to download business config from S3: $($_.Exception.Message)" "ERROR"
        Write-Log "Falling back to default SPF configuration" "WARNING"

        # Fallback to default SPF configuration
        $businessConfig = @{
            businessName = "SanlamLife"
            business_unit = "SPF"
            domain = "mud.internal.co.za"
            basePath = "OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
            serverOUs = @{
                "MSSQL-2022" = "OU=SQL Server,OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
                "Shared-2022" = "OU=Server 2022,OU=Windows Server,OU=Servers,OU=SanlamLife,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
            }
            ad_domain = "MUD"
            environment = "PRD"
            client_tag = "SPF"
        }
    }

    # Save business configuration
    $configPath = "C:\Scripts\business-config.json"
    $businessConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $configPath -Encoding UTF8
    Write-Log "Business configuration saved to: $configPath"

    # Set environment variables for business deployment
    # REPLACE THESE WITH YOUR ACTUAL VALUES OR USE PARAMETER STORE
    [Environment]::SetEnvironmentVariable("SERVER_ROLE", "Shared-2022", "Machine")
    [Environment]::SetEnvironmentVariable("BUSINESS_UNIT", $businessConfig.business_unit, "Machine")
    [Environment]::SetEnvironmentVariable("CLIENT_TAG", $businessConfig.client_tag, "Machine")
    [Environment]::SetEnvironmentVariable("ENVIRONMENT", $businessConfig.environment, "Machine")
    [Environment]::SetEnvironmentVariable("AD_DOMAIN", $businessConfig.ad_domain, "Machine")

    Write-Log "Environment variables set for business deployment"
    
    # Optional: Install additional software based on server role
    $serverRole = $env:SERVER_ROLE  # Get from environment variable set above
    if (!$serverRole) { $serverRole = "Shared-2022" }  # Default role

    Write-Log "Configuring server for role: $serverRole"

    switch ($serverRole) {
        "MSSQL-2022" {
            Write-Log "Preparing for SQL Server 2022 installation"
            # Add SQL Server specific preparations here
            # Note: Actual SQL Server installation should be done separately
        }
        "Shared-2022" {
            Write-Log "Configuring shared server role"
            # Add shared server specific configurations here
        }
        default {
            Write-Log "Using default configuration for role: $serverRole"
        }
    }

    # Create deployment info file
    $deploymentInfo = @{
        DeploymentDate = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        BusinessUnit = $businessConfig.business_unit
        ClientTag = $businessConfig.client_tag
        Environment = $businessConfig.environment
        ServerRole = $serverRole
        AMISource = "Base Windows Server 2022"
        ConfiguredBy = "AWS UserData"
    }

    $deploymentInfoPath = "C:\Scripts\deployment-info.json"
    $deploymentInfo | ConvertTo-Json -Depth 10 | Out-File -FilePath $deploymentInfoPath -Encoding UTF8
    Write-Log "Deployment information saved to: $deploymentInfoPath"
    
    Write-Log "User data execution completed successfully"
    
} catch {
    Write-Log "User data execution failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
}
</powershell>
