# Business Deployment Guide
127214170284
<EMAIL>

This guide explains how to deploy Windows Server instances using the clean base AMI with business-specific configurations.

## Overview

The deployment process follows a two-stage approach:
1. **Base Image**: Windows Server 2022 AMI with .NET 4.8 created by ImageBuilder
2. **Business Configuration**: Dynamically loaded from S3 during instance launch via user data

### S3 Configuration Storage
Business configurations are stored in S3 and dynamically retrieved based on the client tag (SPF, STM, etc.). This approach provides:
- **Centralized Management**: All configurations in one location
- **Dynamic Loading**: No need to hardcode configurations in user data
- **Easy Updates**: Modify configurations without changing deployment scripts
- **Version Control**: S3 versioning for configuration history

## Business Configurations

### Available Configurations

#### SPF (SanlamLife) - Production
- **File**: `configs/SPF_PRD.json`
- **Business Unit**: SPF
- **Domain**: mud.internal.co.za
- **Client Tag**: SPF
- **Server Roles**:
  - `MSSQL-2022`: SQL Server 2022 instances
  - `Shared-2022`: General purpose servers

#### STM (Santam) - Production
- **File**: `configs/STM_PRD.json`
- **Business Unit**: STM
- **Domain**: mud.internal.co.za
- **Client Tag**: STM
- **Server Roles**:
  - `MSSQL-2022`: Database servers
  - `Shared-2022`: General purpose servers

## Deployment Steps

### 1. Upload Business Configurations to S3

#### Option A: Using AWS Console

1. **Create S3 Bucket** (if not exists):
   - Go to **S3** in AWS Console
   - Click **Create bucket**
   - Name: `your-config-bucket` (choose unique name)
   - Region: `Africa (Cape Town) af-south-1`
   - Keep default settings and create

2. **Upload Configuration Files**:
   - Open your S3 bucket
   - Create folder: `business-configs`
   - Upload the JSON files from `configs/` directory:
     - `SPF_PRD.json`
     - `STM_PRD.json`
   - Ensure files are uploaded to `s3://your-bucket/business-configs/`

#### Option B: Using PowerShell Script

```powershell
# Upload configurations to S3
.\deployment\upload-business-configs.ps1 -S3Bucket "your-config-bucket" -Region "af-south-1"
```

This script will:
- Upload all JSON files from the `configs/` directory to S3
- Validate JSON syntax before upload
- Set appropriate permissions
- Create a verification script

### 2. Configure IAM Permissions

Ensure your EC2 instances have the necessary IAM permissions to read from S3:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject"
            ],
            "Resource": [
                "arn:aws:s3:::your-config-bucket/business-configs/*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "ec2:DescribeTags"
            ],
            "Resource": "*"
        }
    ]
}
```

### 3. Prepare User Data Script

Copy the template and update the S3 bucket name:

```powershell
# Copy the template
Copy-Item "templates/user-data-template.ps1" "my-deployment-userdata.ps1"

# Edit the S3 bucket name in the script
# Replace 'your-config-bucket' with your actual S3 bucket name
```

### 4. Set Client Tag

The user data script will automatically determine the business configuration based on:
1. **Environment Variable**: `CLIENT_TAG` (if set)
2. **EC2 Instance Tag**: `ClientTag` (retrieved from instance metadata)
3. **Default Fallback**: SPF (if neither above is available)

### 5. Launch Instance

#### Option A: Using AWS Console

1. **Navigate to EC2**:
   - Go to **EC2** > **Instances** > **Launch instances**

2. **Configure Instance**:
   - **Name**: `SPF-MSSQL-01` (or appropriate name)
   - **AMI**: Select your custom `BaseWindowsServer2022-*` AMI
   - **Instance type**: `t3.medium` or larger
   - **Key pair**: Select your key pair
   - **Network**: Select VPC and subnet
   - **Security group**: Ensure RDP (3389) and WinRM (5986) are allowed

3. **Configure IAM Role**:
   - **IAM instance profile**: Select role with S3 read permissions
   - Required permissions:
     - `s3:GetObject` on `arn:aws:s3:::your-config-bucket/business-configs/*`
     - `ec2:DescribeTags`

4. **Add Tags**:
   - `Name`: `SPF-MSSQL-01`
   - `ClientTag`: `SPF` (or `STM`)
   - `BusinessUnit`: `SPF`
   - `Environment`: `PRD`
   - `ServerRole`: `MSSQL-2022`

5. **User Data**:
   - In **Advanced details** > **User data**
   - Copy content from `templates/user-data-template.ps1`
   - Update the S3 bucket name: `$s3Bucket = "your-config-bucket"`

6. **Launch Instance**

#### Option B: Using AWS CLI

```powershell
# Using AWS CLI with ClientTag for automatic configuration selection
aws ec2 run-instances \
    --image-id ami-xxxxxxxxx \
    --instance-type t3.medium \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --subnet-id subnet-xxxxxxxxx \
    --iam-instance-profile Name=EC2-S3-ReadOnly-Role \
    --user-data file://my-deployment-userdata.ps1 \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=SPF-MSSQL-01},{Key=ClientTag,Value=SPF},{Key=BusinessUnit,Value=SPF},{Key=Environment,Value=PRD},{Key=ServerRole,Value=MSSQL-2022}]'
```

## User Data Script Features

The user data script automatically:

1. **Configures WinRM**: Sets up secure remote management
2. **Saves Business Config**: Creates `/Scripts/business-config.json`
3. **Sets Environment Variables**: For business-specific deployment
4. **Creates Deployment Info**: Logs deployment details
5. **Configures Server Role**: Applies role-specific settings

## Post-Deployment

After the instance launches, you can:

1. **Verify Configuration**:
   ```powershell
   # Check deployment info
   Get-Content C:\Scripts\deployment-info.json | ConvertFrom-Json
   
   # Check business config
   Get-Content C:\Scripts\business-config.json | ConvertFrom-Json
   
   # Check environment variables
   Get-ChildItem Env: | Where-Object Name -like "*BUSINESS*"
   ```

2. **Install Additional Software**:
   - Domain join (separate process)
   - SQL Server installation
   - Business-specific applications

3. **Apply Security Configurations**:
   - Group policies
   - Security baselines
   - Compliance settings

## Best Practices

### Security
- Store sensitive credentials in AWS Parameter Store
- Use IAM roles instead of hardcoded credentials
- Enable CloudTrail for audit logging

### Tagging
- Always tag instances with business unit, environment, and role
- Use consistent naming conventions
- Include cost center tags for billing

### Monitoring
- Enable CloudWatch monitoring
- Set up log forwarding to centralized logging
- Configure alerts for critical metrics

## Troubleshooting

### Common Issues

1. **User Data Not Executing**:
   - Check instance logs in EC2 console
   - Verify PowerShell execution policy
   - Check for syntax errors in user data

2. **WinRM Configuration Fails**:
   - Verify security group allows port 5986
   - Check Windows Firewall settings
   - Validate certificate creation

3. **Business Config Not Applied**:
   - Check user data logs in `C:\Scripts\user-data.log`
   - Verify JSON syntax in business configuration
   - Check file permissions

### Log Locations
- User Data Execution: `C:\Scripts\user-data.log`
- Deployment Info: `C:\Scripts\deployment-info.json`
- Business Config: `C:\Scripts\business-config.json`

## Next Steps

After successful deployment:
1. Configure domain joining (separate process)
2. Install business-specific software
3. Apply security policies
4. Set up monitoring and backup
5. Document the deployment for future reference
